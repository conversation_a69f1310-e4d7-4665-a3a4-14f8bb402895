package com.phodal.remodern.mcp;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phodal.remodern.core.McpTool;
import com.phodal.remodern.core.McpToolResult;
import com.phodal.remodern.tools.bytecode.ByteCodeTool;
import com.phodal.remodern.tools.codegen.AstCodeGenTool;
import com.phodal.remodern.tools.codegen.TemplateCodeGenTool;
import com.phodal.remodern.tools.migration.OpenRewriteTool;
import com.phodal.remodern.tools.parsing.JSPParseTool;
import com.phodal.remodern.tools.parsing.JavaParseTool;
import io.modelcontextprotocol.server.McpServer;
import io.modelcontextprotocol.server.McpSyncServer;
import io.modelcontextprotocol.server.McpServerFeatures;
import io.modelcontextprotocol.server.transport.StdioServerTransportProvider;
import io.modelcontextprotocol.spec.McpSchema;
import io.modelcontextprotocol.spec.McpSchema.CallToolResult;
import io.modelcontextprotocol.spec.McpSchema.ServerCapabilities;
import io.modelcontextprotocol.spec.McpSchema.Tool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * MCP Server implementation for ReModern Java tools using official MCP SDK
 *
 * This implements the standard MCP protocol using the official Java SDK
 */
public class OfficialMcpServer {

    private static final Logger logger = LoggerFactory.getLogger(OfficialMcpServer.class);
    private final List<McpTool> tools;
    private McpSyncServer mcpServer;

    public OfficialMcpServer() {
        this.tools = new ArrayList<>();
        initializeTools();
    }

    private void initializeTools() {
        // Register all available tools
        tools.add(new AstCodeGenTool());
        tools.add(new TemplateCodeGenTool());
        tools.add(new OpenRewriteTool());
        tools.add(new JSPParseTool());
        tools.add(new ByteCodeTool());
        tools.add(new JavaParseTool());

        logger.info("Registered {} MCP tools", tools.size());
    }

    /**
     * Convert our custom McpTool to official SDK Tool Specification
     */
    private McpServerFeatures.SyncToolSpecification createToolSpecification(McpTool tool) {
        // Create the Tool definition
        Tool toolDef = new Tool(
            tool.getName(),
            tool.getDescription(),
            tool.getInputSchema().toString()
        );

        // Create the tool handler
        return new McpServerFeatures.SyncToolSpecification(
            toolDef,
            (exchange, arguments) -> {
                try {
                    // Execute the tool
                    McpToolResult result = tool.execute(arguments);

                    // Convert result to CallToolResult
                    String contentText;
                    boolean isError = !result.isSuccess();

                    if (result.isSuccess()) {
                        contentText = result.getContent() != null ? result.getContent().toString() : "Success";
                    } else {
                        contentText = result.getError() != null ? result.getError() : "Unknown error";
                    }

                    return new CallToolResult(
                        List.of(new McpSchema.TextContent(contentText)),
                        isError
                    );

                } catch (Exception e) {
                    logger.error("Error executing tool {}: {}", tool.getName(), e.getMessage(), e);
                    return new CallToolResult(
                        List.of(new McpSchema.TextContent("Error: " + e.getMessage())),
                        true
                    );
                }
            }
        );
    }

    public void start() throws Exception {
        logger.info("Starting ReModern MCP Server with official SDK");

        // Create transport provider
        StdioServerTransportProvider transportProvider = new StdioServerTransportProvider(new ObjectMapper());

        // Create server capabilities
        ServerCapabilities capabilities = ServerCapabilities.builder()
            .tools(true)  // Enable tool support
            .logging()    // Enable logging support
            .build();

        // Create the MCP server
        mcpServer = McpServer.sync(transportProvider)
            .serverInfo("remodern-java", "1.0.0")
            .capabilities(capabilities)
            .build();

        // Register all tools
        for (McpTool tool : tools) {
            McpServerFeatures.SyncToolSpecification toolSpec = createToolSpecification(tool);
            mcpServer.addTool(toolSpec);
            logger.debug("Registered tool: {}", tool.getName());
        }

        logger.info("MCP Server started successfully with {} tools", tools.size());

        // Keep the server running - the SDK will handle the message loop
        Runtime.getRuntime().addShutdownHook(new Thread(this::stop));

        // The server will run until the process is terminated
        // No need to block here as the SDK handles the message loop
    }

    public void stop() {
        if (mcpServer != null) {
            try {
                mcpServer.close();
                logger.info("MCP server stopped");
            } catch (Exception e) {
                logger.error("Error stopping MCP server", e);
            }
        }
    }

    public static void main(String[] args) {
        // Configure logging to stderr to avoid interfering with JSON-RPC
        System.setProperty("org.slf4j.simpleLogger.defaultLogLevel", "INFO");
        System.setProperty("org.slf4j.simpleLogger.logFile", "System.err");

        OfficialMcpServer server = new OfficialMcpServer();

        try {
            server.start();
        } catch (Exception e) {
            System.err.println("Failed to start server: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }
}
